-- Pizza Job Server Utilities

local Utils = {}

-- Function to log delivery completion with distance info
function Utils.logDeliveryCompletion(playerName, payAmount, xpAmount, distance)
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    local logMessage

    if distance then
        logMessage = string.format("^2[Pizza Job - Delivery Log] %s | Player: %s | Payment: $%d | XP: %d | Distance: %.2f meters^7",
                                 timestamp, playerName, payAmount, xpAmount or 0, distance)
    else
        logMessage = string.format("^2[Pizza Job - Delivery Log] %s | Player: %s | Payment: $%d | XP: %d^7",
                                 timestamp, playerName, payAmount, xpAmount or 0)
    end

    print(logMessage)

    -- You can extend this to write to a file or database if needed
    -- Example: Write to a log file
    -- local logFile = io.open("logs/pizza_deliveries.log", "a")
    -- if logFile then
    --     logFile:write(string.format("[%s] Player: %s completed delivery - Payment: $%d | XP: %d | Distance: %.2f\n",
    --                                timestamp, playerName, payAmount, xpAmount or 0, distance or 0))
    --     logFile:close()
    -- end
end

-- Function to log suspicious activity
function Utils.logSuspiciousActivity(playerName, reason)
    local timestamp = os.date('%Y-%m-%d %H:%M:%S')
    print(string.format("^1[Pizza Job - Security Alert] %s | Player: %s | Reason: %s^7", timestamp, playerName, reason))
    
    -- You can extend this to write to a file or database if needed
    -- Example: Write to a security log file
    -- local logFile = io.open("logs/pizza_security.log", "a")
    -- if logFile then
    --     logFile:write(string.format("[%s] SUSPICIOUS: Player: %s - %s\n", timestamp, playerName, reason))
    --     logFile:close()
    -- end
end

-- Function to format currency (optional utility)
function Utils.formatCurrency(amount)
    return string.format("$%d", amount)
end

-- Function to get current timestamp (optional utility)
function Utils.getCurrentTimestamp()
    return os.date('%Y-%m-%d %H:%M:%S')
end

return Utils
