local QBCore = nil
local ESX = nil

if Config.Framework == 'QB' then
    QBCore = exports['qb-core']:GetCoreObject()
elseif Config.Framework == 'ESX' then
    ESX = exports['es_extended']:getSharedObject()
end

local activeDeliveries = {}
local currentDeliveryLocations = {} -- Store current delivery location for each player
local lastDeliveryTime = {} -- Track last delivery time for each player
local deliveryCooldown = 5000 -- 5 seconds cooldown between deliveries

local Utils = require('server/sv_utils')

-- Function to calculate distance between two points
local function calculateDistance(pos1, pos2)
    local dx = pos1.x - pos2.x
    local dy = pos1.y - pos2.y
    local dz = pos1.z - pos2.z
    return math.sqrt(dx*dx + dy*dy + dz*dz)
end

-- Function to calculate payment based on distance
local function calculatePayment(distance)
    local payment = Config.Pay.baseAmount + (distance * Config.Pay.perMeterRate)
    return math.max(Config.Pay.min, math.min(Config.Pay.max, math.floor(payment)))
end

-- Function to calculate XP based on distance
local function calculateXP(distance)
    local xp = Config.XP.baseAmount + (distance * Config.XP.perMeterRate)
    return math.max(Config.XP.min, math.min(Config.XP.max, math.floor(xp)))
end

-- Function to give pizza job uniform
local function givePizzaUniform(src)
    if not Config.Uniform.enabled then
        return
    end

    print(string.format("^3[Pizza Job] Giving uniform to player %d^7", src))
    TriggerClientEvent('pizzajob:giveUniform', src)
end

local function getIdentifier(Player)
    if Config.Framework == 'QB' then
        return Player.PlayerData.citizenid
    elseif Config.Framework == 'ESX' then
        return Player.getIdentifier()
    end
end

local function checkPlayerExists(identifier, callback)
    MySQL.scalar('SELECT COUNT(*) FROM pizzajob WHERE identifier = ?', {identifier}, function(count)
        callback(count > 0)
    end)
end

local function insertPlayer(identifier)
    MySQL.insert('INSERT INTO pizzajob (identifier, total_deliveries) VALUES (?, ?)', {identifier, 0})
end

local function updateTotalDeliveries(identifier)
    MySQL.update('UPDATE pizzajob SET total_deliveries = total_deliveries + 1 WHERE identifier = ?', {identifier})
end

local function hasRequiredJob(Player)
    if not Config.JobRestriction.enabled then
        return true
    end

    local playerJobName = ""
    if Config.Framework == 'QB' then
        playerJobName = Player.PlayerData.job.name
    elseif Config.Framework == 'ESX' then
        playerJobName = Player.getJob().name
    end

    -- فحص إذا كانت الوظيفة ضمن الوظائف المسموحة
    for _, allowedJob in ipairs(Config.JobRestriction.allowedJobs) do
        if playerJobName == allowedJob then
            return true
        end
    end

    return false
end

-- دالة للتحقق من إمكانية ركوب دباب الدلفري (للرتب المسموحة)
local function canRidePizzaBike(Player)
    if not Config.JobRestriction.enabled then
        return true
    end

    local playerJobName = ""
    if Config.Framework == 'QB' then
        playerJobName = Player.PlayerData.job.name
    elseif Config.Framework == 'ESX' then
        playerJobName = Player.getJob().name
    end

    -- فحص إذا كانت الوظيفة ضمن الوظائف المسموحة لركوب الدباب
    for _, allowedJob in ipairs(Config.JobRestriction.allowedJobs) do
        if playerJobName == allowedJob then
            return true
        end
    end

    return false
end

-- Security function to validate delivery distance
local function isPlayerNearDeliveryLocation(src, deliveryLocation)
    local playerCoords = GetEntityCoords(GetPlayerPed(src))
    local distance = calculateDistance(playerCoords, deliveryLocation)

    -- Allow delivery only if within 10 meters (server-side validation)
    return distance <= 10.0, distance
end

-- Security function to check cooldown
local function isDeliveryCooldownValid(src)
    local currentTime = GetGameTimer()
    local lastTime = lastDeliveryTime[src] or 0

    if currentTime - lastTime < deliveryCooldown then
        return false, math.ceil((deliveryCooldown - (currentTime - lastTime)) / 1000)
    end

    return true, 0
end

RegisterNetEvent('pizzajob:startDelivering')
AddEventHandler('pizzajob:startDelivering', function()
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(src)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(src)
    end

    if Player and hasRequiredJob(Player) then
        -- Check if player has enough money for vehicle insurance
        local playerMoney = 0
        if Config.Framework == 'QB' then
            playerMoney = Player.PlayerData.money.cash or 0
        elseif Config.Framework == 'ESX' then
            playerMoney = Player.getAccount('money').money or 0
        end

        if Config.VehicleInsurance.enabled and playerMoney < Config.VehicleInsurance.amount then
            -- Not enough money for insurance
            TriggerClientEvent('pizzajob:jobCheckResult', src, false)

            if Config.Framework == 'QB' then
                TriggerClientEvent('QBCore:Notify', src, string.format(Config.VehicleInsurance.messages.notEnoughMoney, Config.VehicleInsurance.amount), 'error')
            elseif Config.Framework == 'ESX' then
                TriggerClientEvent('esx:showNotification', src, string.format(Config.VehicleInsurance.messages.notEnoughMoney, Config.VehicleInsurance.amount), 'error')
            end
            return
        end

        -- Charge vehicle insurance
        if Config.VehicleInsurance.enabled then
            if Config.Framework == 'QB' then
                Player.Functions.RemoveMoney("cash", Config.VehicleInsurance.amount, "تأمين دراجة التوصيل")
            elseif Config.Framework == 'ESX' then
                Player.removeAccountMoney('money', Config.VehicleInsurance.amount)
            end

            print(string.format("^3[Pizza Job] Vehicle insurance charged: $%d for player %d^7", Config.VehicleInsurance.amount, src))

            -- Notify player about insurance charge
            if Config.Framework == 'QB' then
                TriggerClientEvent('QBCore:Notify', src, string.format(Config.VehicleInsurance.messages.charged, Config.VehicleInsurance.amount), 'primary')
            elseif Config.Framework == 'ESX' then
                TriggerClientEvent('esx:showNotification', src, string.format(Config.VehicleInsurance.messages.charged, Config.VehicleInsurance.amount), 'info')
            end
        end

        activeDeliveries[src] = true
        TriggerClientEvent('pizzajob:jobCheckResult', src, true)

        -- Give uniform when starting delivery
        if Config.Uniform.enabled then
            givePizzaUniform(src)
            print(string.format("^2[Pizza Job] Uniform given to player %d when starting delivery^7", src))
        end
    else
        TriggerClientEvent('pizzajob:jobCheckResult', src, false)
    end
end)

RegisterNetEvent('pizzajob:stopDelivering')
AddEventHandler('pizzajob:stopDelivering', function()
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(src)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(src)
    end

    -- Refund vehicle insurance if enabled
    if Config.VehicleInsurance.enabled and Config.VehicleInsurance.refundOnReturn and Player then
        if Config.Framework == 'QB' then
            Player.Functions.AddMoney("cash", Config.VehicleInsurance.amount, "استرداد تأمين دراجة التوصيل")
        elseif Config.Framework == 'ESX' then
            Player.addAccountMoney('money', Config.VehicleInsurance.amount)
        end

        print(string.format("^2[Pizza Job] Vehicle insurance refunded: $%d for player %d^7", Config.VehicleInsurance.amount, src))

        -- Notify player about insurance refund
        if Config.Framework == 'QB' then
            TriggerClientEvent('QBCore:Notify', src, string.format(Config.VehicleInsurance.messages.refunded, Config.VehicleInsurance.amount), 'success')
        elseif Config.Framework == 'ESX' then
            TriggerClientEvent('esx:showNotification', src, string.format(Config.VehicleInsurance.messages.refunded, Config.VehicleInsurance.amount), 'success')
        end
    end

    activeDeliveries[src] = nil
    currentDeliveryLocations[src] = nil

    -- Restore original clothes when stopping delivery
    if Config.Uniform.enabled then
        TriggerClientEvent('pizzajob:restoreClothes', src)
        print(string.format("^2[Pizza Job] Original clothes restored for player %d when stopping delivery^7", src))
    end
end)

-- Event to store delivery location when player starts a delivery
RegisterNetEvent('pizzajob:setDeliveryLocation')
AddEventHandler('pizzajob:setDeliveryLocation', function(deliveryLocation)
    local src = source

    -- Security validation for delivery location
    if not activeDeliveries[src] then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, "Attempted to set delivery location without active delivery.")
        print("^1[Pizza Job] Security: Invalid delivery location set attempt by player " .. src .. "^7")
        return
    end

    -- Validate delivery location format
    if not deliveryLocation or type(deliveryLocation) ~= "vector3" then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, "Attempted to set invalid delivery location format.")
        print("^1[Pizza Job] Security: Invalid delivery location format from player " .. src .. "^7")
        return
    end

    -- Validate coordinates are within reasonable bounds (Los Santos map)
    if deliveryLocation.x < -4000 or deliveryLocation.x > 4000 or
       deliveryLocation.y < -4000 or deliveryLocation.y > 4000 or
       deliveryLocation.z < -200 or deliveryLocation.z > 2000 then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, string.format("Attempted to set delivery location outside map bounds: %s", json.encode(deliveryLocation)))
        print("^1[Pizza Job] Security: Out of bounds delivery location from player " .. src .. "^7")
        return
    end

    currentDeliveryLocations[src] = deliveryLocation
    print(string.format("^3[Pizza Job] Delivery location set for player %d: %s^7", src, json.encode(deliveryLocation)))
end)

RegisterNetEvent('neon_pizzajob:completeDelivery')
AddEventHandler('neon_pizzajob:completeDelivery', function()
    local src = source
    local Player

    print("^2[Pizza Job] Delivery completion started for player: " .. src .. "^7")

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(src)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(src)
    end

    -- Security Check 1: Player and active delivery validation
    if not Player or not activeDeliveries[src] then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, "Attempted delivery completion without being active or valid player.")
        print("^1[Pizza Job] Security: Invalid delivery attempt by player " .. src .. "^7")
        return
    end

    -- Security Check 2: Job validation
    if not hasRequiredJob(Player) then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, "Attempted delivery completion without required job.")
        print("^1[Pizza Job] Security: Job validation failed for player " .. src .. "^7")
        return
    end

    -- Security Check 3: Cooldown validation
    local isCooldownValid, remainingTime = isDeliveryCooldownValid(src)
    if not isCooldownValid then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, string.format("Attempted delivery completion during cooldown (%d seconds remaining).", remainingTime))
        print("^1[Pizza Job] Security: Cooldown violation by player " .. src .. " (" .. remainingTime .. "s remaining)^7")
        return
    end

    -- Security Check 4: Delivery location validation
    if not currentDeliveryLocations[src] then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, "Attempted delivery completion without valid delivery location.")
        print("^1[Pizza Job] Security: No delivery location for player " .. src .. "^7")
        return
    end

    -- Security Check 5: Distance validation
    local isNearLocation, actualDistance = isPlayerNearDeliveryLocation(src, currentDeliveryLocations[src])
    if not isNearLocation then
        local steamName = GetPlayerName(src)
        Utils.logSuspiciousActivity(steamName, string.format("Attempted delivery completion from distance %.2f meters (max allowed: 10m).", actualDistance))
        print("^1[Pizza Job] Security: Distance validation failed for player " .. src .. " (distance: " .. actualDistance .. "m)^7")
        return
    end

    -- All security checks passed, proceed with delivery completion
    print("^2[Pizza Job] All security checks passed for player " .. src .. "^7")
        -- Calculate rewards based on distance
        local payAmount = Config.Pay.baseAmount -- Default payment
        local xpAmount = Config.XP.baseAmount -- Default XP

        if currentDeliveryLocations[src] then
            -- Chef location
            local chefLocation = vector3(Config.Ped.location.x, Config.Ped.location.y, Config.Ped.location.z)
            local deliveryLocation = currentDeliveryLocations[src]

            -- Calculate distance
            local distance = calculateDistance(chefLocation, deliveryLocation)

            -- Calculate rewards based on distance
            payAmount = calculatePayment(distance)
            xpAmount = calculateXP(distance)

            print(string.format("^2[Pizza Job] Distance: %.2f meters | Money: %d | XP: %d^7", distance, payAmount, xpAmount))
        else
            print("^1[Pizza Job] Warning: No delivery location found, using default rewards^7")
        end

        print("^2[Pizza Job] Giving reward - Money: " .. payAmount .. " | XP: " .. xpAmount .. "^7")

        -- Send distance info with XP notification if available
        local distanceForNotification = nil
        if currentDeliveryLocations[src] and Config.DistanceRewards.showDistance then
            local chefLocation = vector3(Config.Ped.location.x, Config.Ped.location.y, Config.Ped.location.z)
            distanceForNotification = calculateDistance(chefLocation, currentDeliveryLocations[src])
        end

        if Config.Framework == 'QB' then
            Player.Functions.AddMoney("cash", payAmount, "الدلفري")
            -- إضافة XP للاعب QB (إذا كان نظام XP متوفر)
            if exports['qb-core'] and exports['qb-core'].AddPlayerXP then
                exports['qb-core']:AddPlayerXP(src, xpAmount)
            end
            -- إضافة إشعار XP للعميل QB
            TriggerClientEvent('pizzajob:showXP', src, xpAmount, 'delivery', distanceForNotification)
            -- إضافة إشعار المال
            TriggerClientEvent('pizzajob:showPayment', src, payAmount, distanceForNotification)
            print("^2[Pizza Job] Money and XP added successfully for QB: $" .. payAmount .. " | XP: " .. xpAmount .. "^7")
        elseif Config.Framework == 'ESX' then
            Player.addAccountMoney('money', payAmount)
            TriggerEvent('SvStore_xplevel:updateCurrentPlayerXP', source, 'add', xpAmount)
            -- إضافة إشعار XP للعميل ESX
            TriggerClientEvent('pizzajob:showXP', src, xpAmount, 'delivery', distanceForNotification)
            -- إضافة إشعار المال
            TriggerClientEvent('pizzajob:showPayment', src, payAmount, distanceForNotification)
            print("^2[Pizza Job] Money and XP added successfully for ESX: $" .. payAmount .. " | XP: " .. xpAmount .. "^7")
        end

        local steamName = GetPlayerName(src)

        -- Send notification about rewards
        if Config.Framework == 'QB' then
            TriggerClientEvent('QBCore:Notify', src, string.format('تم تسليم الطلب! حصلت على $%d و %d نقطة خبرة', payAmount, xpAmount), 'success')
        elseif Config.Framework == 'ESX' then
            TriggerClientEvent('esx:showNotification', src, string.format('تم تسليم الطلب! حصلت على $%d و %d نقطة خبرة', payAmount, xpAmount))
        end

        Utils.logDeliveryCompletion(steamName, payAmount, xpAmount, distanceForNotification)

        local identifier = getIdentifier(Player)
        checkPlayerExists(identifier, function(exists)
            if exists then
                updateTotalDeliveries(identifier)
            else
                insertPlayer(identifier)
                updateTotalDeliveries(identifier)
            end
        end)

        activeDeliveries[src] = true
        -- Update last delivery time for cooldown
        lastDeliveryTime[src] = GetGameTimer()
        -- Clean up delivery location after completion
        currentDeliveryLocations[src] = nil

        print("^2[Pizza Job] Delivery completed successfully for player " .. src .. "^7")
    -- Note: The old else block is removed as security checks are now handled above
end)

RegisterNetEvent('pizzajob:requestLeaderboard')
AddEventHandler('pizzajob:requestLeaderboard', function()
    local src = source
    MySQL.query('SELECT identifier, total_deliveries FROM pizzajob ORDER BY total_deliveries DESC LIMIT 10', {}, function(results)
        local leaderboard = {}

        for _, row in ipairs(results) do
            local identifier = row.identifier
            local firstname, lastname = "Unknown", ""

            if Config.Framework == 'QB' then
                local player = QBCore.Functions.GetPlayerByCitizenId(identifier)
                if player then
                    firstname = player.PlayerData.charinfo.firstname
                    lastname = player.PlayerData.charinfo.lastname
                end
            elseif Config.Framework == 'ESX' then
                local player = ESX.GetPlayerFromIdentifier(identifier)
                if player then
                    firstname = player.get('firstName') or "Unknown"
                    lastname = player.get('lastName') or ""
                end
            end

            table.insert(leaderboard, {
                identifier = identifier,
                firstname = firstname,
                lastname = lastname,
                total_deliveries = row.total_deliveries
            })
        end

        TriggerClientEvent('pizzajob:receiveLeaderboard', src, leaderboard)
    end)
end)





RegisterNetEvent('pizzajob:checkJob')
AddEventHandler('pizzajob:checkJob', function()
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(src)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(src)
    end

    if Player then
        local hasJob = hasRequiredJob(Player)
        TriggerClientEvent('pizzajob:jobCheckResult', src, hasJob)

        -- Note: Uniform is now given when starting delivery, not on login
    else
        TriggerClientEvent('pizzajob:jobCheckResult', src, false)
    end
end)

-- Event to handle unauthorized vehicle use
RegisterNetEvent('pizzajob:checkUnauthorizedVehicleUse')
AddEventHandler('pizzajob:checkUnauthorizedVehicleUse', function(suspectPlayerId, vehicle)
    local src = source
    local suspectPlayer

    if Config.Framework == 'QB' then
        suspectPlayer = QBCore.Functions.GetPlayer(suspectPlayerId)
    elseif Config.Framework == 'ESX' then
        suspectPlayer = ESX.GetPlayerFromId(suspectPlayerId)
    end

    if suspectPlayer then
        local canRide = canRidePizzaBike(suspectPlayer)
        if not canRide then
            -- Force player out of vehicle
            TriggerClientEvent('pizzajob:forceExitVehicle', suspectPlayerId)

            -- Notify the unauthorized player
            if Config.Framework == 'QB' then
                TriggerClientEvent('QBCore:Notify', suspectPlayerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            elseif Config.Framework == 'ESX' then
                TriggerClientEvent('esx:showNotification', suspectPlayerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            end

            -- Log the incident
            local steamName = GetPlayerName(suspectPlayerId)
            Utils.logSuspiciousActivity(steamName, "Attempted to use delivery vehicle without authorized job.")
            print("^1[Pizza Job] Security: Unauthorized vehicle use by player " .. suspectPlayerId .. "^7")
        end
    end
end)

-- New event to check vehicle access when trying to enter
RegisterNetEvent('pizzajob:checkVehicleAccess')
AddEventHandler('pizzajob:checkVehicleAccess', function(playerId, vehicle)
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(playerId)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(playerId)
    end

    if Player then
        local canRide = canRidePizzaBike(Player)
        if not canRide then
            -- Prevent entry and notify
            TriggerClientEvent('pizzajob:preventVehicleEntry', playerId)

            -- Notify the unauthorized player
            if Config.Framework == 'QB' then
                TriggerClientEvent('QBCore:Notify', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            elseif Config.Framework == 'ESX' then
                TriggerClientEvent('esx:showNotification', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            end

            -- Log the incident
            local steamName = GetPlayerName(playerId)
            Utils.logSuspiciousActivity(steamName, "Attempted to enter pizza delivery vehicle without authorized job.")
            print(string.format("^1[Pizza Job] Security: Player %d (%s) attempted to enter pizza vehicle without authorized job^7", playerId, steamName))
        end
    end
end)

-- New event to validate current vehicle driver
RegisterNetEvent('pizzajob:validateVehicleDriver')
AddEventHandler('pizzajob:validateVehicleDriver', function(playerId, vehicle)
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(playerId)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(playerId)
    end

    if Player then
        local canRide = canRidePizzaBike(Player)
        if not canRide then
            -- Force player out of vehicle
            TriggerClientEvent('pizzajob:forceExitVehicle', playerId)

            -- Notify the unauthorized player
            if Config.Framework == 'QB' then
                TriggerClientEvent('QBCore:Notify', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            elseif Config.Framework == 'ESX' then
                TriggerClientEvent('esx:showNotification', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
            end

            -- Log the incident
            local steamName = GetPlayerName(playerId)
            Utils.logSuspiciousActivity(steamName, "Driving pizza delivery vehicle without authorized job.")
            print(string.format("^1[Pizza Job] Security: Player %d (%s) driving pizza vehicle without authorized job^7", playerId, steamName))
        end
    end
end)

-- Clean up player data on disconnect
AddEventHandler('playerDropped', function(reason)
    local src = source

    -- Clean up all player-related data
    activeDeliveries[src] = nil
    currentDeliveryLocations[src] = nil
    lastDeliveryTime[src] = nil

    print("^3[Pizza Job] Cleaned up data for disconnected player " .. src .. "^7")
end)

-- Event to manually give uniform (can be triggered by admin or other scripts)
RegisterNetEvent('pizzajob:requestUniform')
AddEventHandler('pizzajob:requestUniform', function()
    local src = source
    local Player

    if Config.Framework == 'QB' then
        Player = QBCore.Functions.GetPlayer(src)
    elseif Config.Framework == 'ESX' then
        Player = ESX.GetPlayerFromId(src)
    end

    if Player and hasRequiredJob(Player) then
        givePizzaUniform(src)
        print(string.format("^2[Pizza Job] Uniform manually given to player %d^7", src))
    else
        print(string.format("^1[Pizza Job] Player %d requested uniform but doesn't have the job^7", src))
    end
end)

-- Enhanced vehicle protection system - periodic check for all pizza vehicles
Citizen.CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds

        local players = GetPlayers()
        for _, playerId in ipairs(players) do
            local playerPed = GetPlayerPed(playerId)
            if playerPed and DoesEntityExist(playerPed) then
                local vehicle = GetVehiclePedIsIn(playerPed, false)
                if vehicle and DoesEntityExist(vehicle) then
                    local plate = GetVehicleNumberPlateText(vehicle)
                    -- Check if it's a pizza delivery vehicle
                    if plate and string.find(plate, "PIZZA") then
                        local Player
                        if Config.Framework == 'QB' then
                            Player = QBCore.Functions.GetPlayer(playerId)
                        elseif Config.Framework == 'ESX' then
                            Player = ESX.GetPlayerFromId(playerId)
                        end

                        if Player then
                            local canRide = canRidePizzaBike(Player)
                            if not canRide then
                                -- Force player out of vehicle
                                TriggerClientEvent('pizzajob:forceExitVehicle', playerId)

                                -- Notify the unauthorized player
                                if Config.Framework == 'QB' then
                                    TriggerClientEvent('QBCore:Notify', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
                                elseif Config.Framework == 'ESX' then
                                    TriggerClientEvent('esx:showNotification', playerId, 'هذه الدراجة مخصصة للدلفري والإدارة والميكانيك فقط!', 'error')
                                end

                                -- Log the incident
                                local steamName = GetPlayerName(playerId)
                                Utils.logSuspiciousActivity(steamName, "Found in pizza delivery vehicle without authorized job during periodic check.")
                                print(string.format("^1[Pizza Job] Security: Player %d (%s) found in pizza vehicle without authorized job (periodic check)^7", playerId, steamName))
                            end
                        end
                    end
                end
            end
        end
    end
end)

