local pedModel = Config.Ped.model

local isDelivering = false
local currentDelivery = nil
local targetZoneId = nil
local currentDeliveryBlip = nil
local currentDeliveryCircle = nil -- For the yellow circle around delivery location
local deliveryCircleThread = nil -- Thread for drawing the circle
local playerVehicles = {}
local deliveryBots = {}
local currentDeliveryBot = nil
local hasJobAccess = false
local hasActiveDeliveryVehicle = false
local currentDeliveryVehicle = nil
local lastDeliveryTime = 0 -- For cooldown protection
local deliveryCooldown = 5000 -- 5 seconds cooldown between deliveries
local vehicleOwner = nil -- Track who owns the current delivery vehicle
local lastVehicleUseTime = 0 -- Track last time vehicle was used
local vehicleInactivityTimer = 900000 -- 15 minutes in milliseconds (15 * 60 * 1000)
local deliveryStartTime = 0 -- Track when delivery started
local lastDeliveryCompletionTime = 0 -- Track when last delivery was completed
local maxDeliveryIdleTime = 600000 -- 10 minutes max idle time without completing delivery (10 * 60 * 1000)
local abandonedVehicles = {} -- Track abandoned vehicles and their abandonment time

local function getRandomDeliveryLocation()
    local locations = Config.Deliveries.locations
    return locations[math.random(1, #locations)]
end

-- Security function to check if player is near delivery location
local function isPlayerNearDeliveryLocation()
    if not currentDelivery or not currentDelivery.location then
        return false
    end

    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local deliveryCoords = currentDelivery.location
    local distance = #(playerCoords - deliveryCoords)

    -- Allow delivery only if within 5 meters of the delivery location
    return distance <= 5.0
end

-- Security function to check if player has the correct delivery vehicle
local function hasCorrectDeliveryVehicle()
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)

    if vehicle == 0 then
        -- Player is not in a vehicle, check if they have the delivery vehicle nearby
        vehicle = GetVehiclePedIsIn(playerPed, true) -- Last vehicle
        if vehicle == 0 or not DoesEntityExist(vehicle) then
            return false
        end
    end

    -- Check if it's the correct delivery vehicle
    return currentDeliveryVehicle and vehicle == currentDeliveryVehicle
end

-- Security function to check player state
local function isPlayerStateValid()
    local playerPed = PlayerPedId()

    -- Check if player is dead
    if IsEntityDead(playerPed) then
        return false, "لا يمكنك التسليم وأنت ميت!"
    end

    -- Check if player is in combat
    if IsPedInCombat(playerPed, 0) then
        return false, "لا يمكنك التسليم أثناء القتال!"
    end

    -- Check if player is falling or in air
    if IsPedFalling(playerPed) then
        return false, "لا يمكنك التسليم أثناء السقوط!"
    end

    return true, nil
end

-- Function to protect delivery vehicle from unauthorized use
local function protectDeliveryVehicle(vehicle, ownerServerId)
    Citizen.CreateThread(function()
        while DoesEntityExist(vehicle) and currentDeliveryVehicle == vehicle do
            local playerPed = PlayerPedId()
            local playerServerId = GetPlayerServerId(PlayerId())

            -- Check if someone is trying to enter the vehicle
            if IsPedGettingIntoAVehicle(playerPed) then
                local targetVehicle = GetVehiclePedIsTryingToEnter(playerPed)
                if targetVehicle == vehicle and playerServerId ~= ownerServerId then
                    -- Always check with server for job access (more secure)
                    TriggerServerEvent('pizzajob:checkVehicleAccess', playerServerId, vehicle)
                end
            end

            -- Check if someone unauthorized is in the vehicle
            local driver = GetPedInVehicleSeat(vehicle, -1)
            if driver ~= 0 and driver ~= playerPed then
                local driverServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(driver))
                if driverServerId ~= ownerServerId then
                    -- Force them out if they don't have job access
                    TriggerServerEvent('pizzajob:checkUnauthorizedVehicleUse', driverServerId, vehicle)
                end
            end

            Wait(500) -- Check more frequently for better protection
        end
    end)
end

-- Global vehicle protection for all pizza delivery vehicles
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local playerServerId = GetPlayerServerId(PlayerId())

        -- Check if player is trying to enter any vehicle
        if IsPedGettingIntoAVehicle(playerPed) then
            local targetVehicle = GetVehiclePedIsTryingToEnter(playerPed)
            if DoesEntityExist(targetVehicle) then
                local plate = GetVehicleNumberPlateText(targetVehicle)
                -- Check if it's a pizza delivery vehicle (plates start with "PIZZA")
                if plate and string.find(plate, "PIZZA") then
                    -- Always check with server - don't rely on local hasJobAccess
                    TriggerServerEvent('pizzajob:checkVehicleAccess', playerServerId, targetVehicle)
                end
            end
        end

        -- Check if player is currently in a pizza delivery vehicle
        if IsPedInAnyVehicle(playerPed, false) then
            local vehicle = GetVehiclePedIsIn(playerPed, false)
            if DoesEntityExist(vehicle) then
                local plate = GetVehicleNumberPlateText(vehicle)
                -- Check if it's a pizza delivery vehicle
                if plate and string.find(plate, "PIZZA") then
                    local driver = GetPedInVehicleSeat(vehicle, -1)
                    if driver == playerPed then -- Player is the driver
                        TriggerServerEvent('pizzajob:validateVehicleDriver', playerServerId, vehicle)
                    end
                end
            end
        end

        Wait(500) -- Check more frequently for better protection
    end
end)

-- Additional protection: Check for any pizza vehicle interaction
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local playerServerId = GetPlayerServerId(PlayerId())

        -- Get all vehicles in range
        local playerCoords = GetEntityCoords(playerPed)
        local vehicles = GetGamePool('CVehicle')

        for _, vehicle in ipairs(vehicles) do
            if DoesEntityExist(vehicle) then
                local vehicleCoords = GetEntityCoords(vehicle)
                local distance = #(playerCoords - vehicleCoords)

                -- Only check vehicles within 10 meters
                if distance <= 10.0 then
                    local plate = GetVehicleNumberPlateText(vehicle)
                    if plate and string.find(plate, "PIZZA") then
                        -- Check if player is near the vehicle and trying to interact
                        if distance <= 3.0 then
                            local driver = GetPedInVehicleSeat(vehicle, -1)
                            if driver == playerPed then
                                -- Player is driving a pizza vehicle, validate
                                TriggerServerEvent('pizzajob:validateVehicleDriver', playerServerId, vehicle)
                            end
                        end
                    end
                end
            end
        end

        Wait(2000) -- Check every 2 seconds for nearby vehicles
    end
end)

-- Function to monitor vehicle inactivity
local function monitorVehicleInactivity(vehicle, ownerServerId)
    Citizen.CreateThread(function()
        while DoesEntityExist(vehicle) and currentDeliveryVehicle == vehicle do
            local playerPed = PlayerPedId()
            local playerServerId = GetPlayerServerId(PlayerId())

            if playerServerId == ownerServerId then
                local isPlayerInVehicle = IsPedInVehicle(playerPed, vehicle, false)
                local currentTime = GetGameTimer()

                if isPlayerInVehicle then
                    lastVehicleUseTime = currentTime
                end

                -- Check for vehicle inactivity (15 minutes without using vehicle)
                if currentTime - lastVehicleUseTime > vehicleInactivityTimer then
                    if Config.Framework == 'QB' then
                        TriggerEvent('QBCore:Notify', 'تم حذف الدراجة بسبب عدم الاستخدام لمدة 15 دقيقة', 'error')
                    elseif Config.Framework == 'ESX' then
                        TriggerEvent('esx:showNotification', 'تم حذف الدراجة بسبب عدم الاستخدام لمدة 15 دقيقة', 'error')
                    end

                    -- Force stop delivery and remove vehicle
                    stopDelivering()
                    break
                end

                -- Check for delivery completion timeout (10 minutes without completing any delivery)
                if isDelivering and lastDeliveryCompletionTime > 0 and currentTime - lastDeliveryCompletionTime > maxDeliveryIdleTime then
                    if Config.Framework == 'QB' then
                        TriggerEvent('QBCore:Notify', 'تم حذف الدراجة بسبب عدم تسليم أي طلب لمدة 10 دقائق', 'error')
                    elseif Config.Framework == 'ESX' then
                        TriggerEvent('esx:showNotification', 'تم حذف الدراجة بسبب عدم تسليم أي طلب لمدة 10 دقائق', 'error')
                    end

                    stopDelivering()
                    break
                end
            end

            Wait(10000) -- Check every 10 seconds
        end
    end)
end

-- Global monitoring for abandoned vehicles (vehicles that no one is using)
Citizen.CreateThread(function()
    while true do
        Wait(30000) -- Check every 30 seconds

        -- Get all vehicles in the world
        local vehicles = GetGamePool('CVehicle')

        for _, vehicle in ipairs(vehicles) do
            if DoesEntityExist(vehicle) then
                local plate = GetVehicleNumberPlateText(vehicle)
                -- Check if it's a pizza delivery vehicle
                if plate and string.find(plate, "PIZZA") then
                    local driver = GetPedInVehicleSeat(vehicle, -1)
                    local hasAnyPassenger = false

                    -- Check all seats for passengers
                    for seat = -1, GetVehicleMaxNumberOfPassengers(vehicle) - 1 do
                        if GetPedInVehicleSeat(vehicle, seat) ~= 0 then
                            hasAnyPassenger = true
                            break
                        end
                    end

                    -- If vehicle is completely empty
                    if not hasAnyPassenger then
                        -- Check if this vehicle has been tracked for abandonment
                        local vehicleId = tostring(vehicle)
                        if not abandonedVehicles then
                            abandonedVehicles = {}
                        end

                        if not abandonedVehicles[vehicleId] then
                            -- Start tracking this abandoned vehicle
                            abandonedVehicles[vehicleId] = GetGameTimer()
                        else
                            -- Check if it's been abandoned for 15 minutes
                            local abandonedTime = GetGameTimer() - abandonedVehicles[vehicleId]
                            if abandonedTime > vehicleInactivityTimer then -- 15 minutes
                                -- Delete the abandoned vehicle
                                DeleteVehicle(vehicle)
                                abandonedVehicles[vehicleId] = nil
                                print("^3[Pizza Job] Abandoned delivery vehicle deleted after 15 minutes^7")
                            end
                        end
                    else
                        -- Vehicle has someone in it, remove from abandoned list
                        local vehicleId = tostring(vehicle)
                        if abandonedVehicles and abandonedVehicles[vehicleId] then
                            abandonedVehicles[vehicleId] = nil
                        end
                    end
                end
            end
        end
    end
end)

-- Function to make bot face and look at player
local function startBotLookAtPlayer(bot)
    Citizen.CreateThread(function()
        while DoesEntityExist(bot) and currentDeliveryBot == bot do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local botCoords = GetEntityCoords(bot)
            local distance = #(playerCoords - botCoords)

            -- Only face player if they are close enough (within 10 meters)
            if distance <= 10.0 then
                -- Use TaskTurnPedToFaceEntity for smooth turning
                TaskTurnPedToFaceEntity(bot, playerPed, 2000)

                -- Also make bot look at player with head
                TaskLookAtEntity(bot, playerPed, -1, 2048, 3)
            end

            Wait(1000) -- Check every second
        end
    end)
end

local function createDeliveryBot(coords, heading)
    -- Only create bot if player has job access
    if not hasJobAccess then
        return nil
    end

    local botHash = GetHashKey(Config.DeliveryBot.model)

    RequestModel(botHash)
    while not HasModelLoaded(botHash) do
        Wait(500)
    end

    -- Use provided heading or default to 0.0
    local botHeading = heading or 0.0
    local bot = CreatePed(4, botHash, coords.x, coords.y, coords.z - 1.0, botHeading, false, true)
    SetEntityInvincible(bot, true)
    SetEntityCollision(bot, true, true)
    SetBlockingOfNonTemporaryEvents(bot, true)
    -- Keep bot in place but allow rotation
    SetEntityCanBeDamaged(bot, false)
    SetPedCanRagdoll(bot, false)

    -- Start bot look at player thread
    startBotLookAtPlayer(bot)

    return bot
end

local function removeDeliveryBot()
    if currentDeliveryBot and DoesEntityExist(currentDeliveryBot) then
        -- Stop the look at task
        ClearPedTasks(currentDeliveryBot)

        DeleteEntity(currentDeliveryBot)
        currentDeliveryBot = nil
    end
end

-- Function to remove delivery circle
local function removeDeliveryCircle()
    if currentDeliveryCircle then
        RemoveBlip(currentDeliveryCircle)
        currentDeliveryCircle = nil
    end

    if deliveryCircleThread then
        deliveryCircleThread = nil
    end

    -- Hide any text UI that might be showing
    lib.hideTextUI()
end

function returnDeliveryVehicle()
    if not hasActiveDeliveryVehicle or not currentDeliveryVehicle then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'لا يوجد دباب للتسليم', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'لا يوجد دباب للتسليم', 'error')
        end
        return
    end

    if lib.progressCircle({
        duration = 3000,
        position = 'bottom',
        label = 'جاري تسليم الدباب',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
            mouse = false
        }
    }) then
        if DoesEntityExist(currentDeliveryVehicle) then
            DeleteVehicle(currentDeliveryVehicle)
        end

        hasActiveDeliveryVehicle = false
        currentDeliveryVehicle = nil
        playerVehicles[PlayerPedId()] = nil

        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'تم تسليم الدباب بنجاح. يمكنك الآن طلب دباب جديد.', 'success')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'تم تسليم الدباب بنجاح. يمكنك الآن طلب دباب جديد.', 'success')
        end

        lib.hideTextUI()
    end
end

function startDelivering()
    -- Check if already delivering or has active vehicle
    if isDelivering or hasActiveDeliveryVehicle then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'أنت تقوم بالتوصيل بالفعل!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'أنت تقوم بالتوصيل بالفعل!', 'error')
        end
        return
    end

    local vehicleHash = GetHashKey(Config.Deliveries.vehicle)
    local uniquePlate = "PIZZA" .. tostring(math.random(100, 999))

    RequestModel(vehicleHash)
    while not HasModelLoaded(vehicleHash) do
        Wait(500)
    end

    local vehicle = CreateVehicle(vehicleHash, Config.Deliveries.spawnLocation.x, Config.Deliveries.spawnLocation.y, Config.Deliveries.spawnLocation.z, Config.Deliveries.spawnLocation.w, true, false)
    SetVehicleNumberPlateText(vehicle, uniquePlate)
    SetVehicleOnGroundProperly(vehicle)
    SetEntityAsMissionEntity(vehicle, true, true)

    -- Lock the vehicle to prevent unauthorized access
    SetVehicleDoorsLocked(vehicle, 2) -- Lock for everyone except the owner

    -- Set vehicle as owned by this player
    SetVehicleHasBeenOwnedByPlayer(vehicle, true)

    -- Register as active delivery vehicle
    playerVehicles[PlayerPedId()] = vehicle
    currentDeliveryVehicle = vehicle
    hasActiveDeliveryVehicle = true

    -- Set vehicle owner and protection
    local playerServerId = GetPlayerServerId(PlayerId())
    vehicleOwner = playerServerId
    lastVehicleUseTime = GetGameTimer()
    deliveryStartTime = GetGameTimer()
    lastDeliveryCompletionTime = GetGameTimer() -- Start counting from when delivery begins

    -- Start vehicle protection and monitoring
    protectDeliveryVehicle(vehicle, playerServerId)
    monitorVehicleInactivity(vehicle, playerServerId)

    -- Unlock the vehicle for the owner only
    SetVehicleDoorsLocked(vehicle, 0) -- Unlock for the owner

    TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)

    -- Lock the vehicle again after the player gets in
    Citizen.SetTimeout(2000, function()
        if DoesEntityExist(vehicle) then
            SetVehicleDoorsLocked(vehicle, 2) -- Lock for everyone except owner
        end
    end)

    isDelivering = true
    TriggerServerEvent('pizzajob:startDelivering')
    currentDelivery = getRandomDeliveryLocation()

    -- Send delivery location to server for distance calculation
    TriggerServerEvent('pizzajob:setDeliveryLocation', currentDelivery.location)

    setDeliveryWaypoint(currentDelivery)
    createDeliveryBlip(currentDelivery)
end



function setDeliveryWaypoint(delivery)
    if delivery and delivery.location then
        SetNewWaypoint(delivery.location.x, delivery.location.y)
    end
end

-- Function to create delivery area circle
local function createDeliveryCircle(coords)
    if not Config.DeliveryCircle.enabled then
        return
    end

    removeDeliveryCircle() -- Remove any existing circle first

    -- Create blip for the circle area if enabled
    if Config.DeliveryCircle.showOnMap then
        currentDeliveryCircle = AddBlipForRadius(coords.x, coords.y, coords.z, Config.DeliveryCircle.radius)
        SetBlipColour(currentDeliveryCircle, 5) -- Yellow color
        SetBlipAlpha(currentDeliveryCircle, Config.DeliveryCircle.color.a)
    end

    -- Start 3D circle drawing thread if enabled
    if Config.DeliveryCircle.showIn3D then
        deliveryCircleThread = Citizen.CreateThread(function()
            while currentDeliveryCircle and isDelivering do
                local playerCoords = GetEntityCoords(PlayerPedId())
                local distance = #(playerCoords - coords)

                -- Only draw if player is within reasonable distance (to save performance)
                if distance <= 200.0 then
                    -- Draw circle on ground
                    DrawMarker(
                        1, -- Circle marker type
                        coords.x, coords.y, coords.z - 1.0, -- Position (slightly below ground)
                        0.0, 0.0, 0.0, -- Direction
                        0.0, 0.0, 0.0, -- Rotation
                        Config.DeliveryCircle.radius * 2.0, Config.DeliveryCircle.radius * 2.0, 1.0, -- Scale
                        Config.DeliveryCircle.color.r, Config.DeliveryCircle.color.g, Config.DeliveryCircle.color.b, Config.DeliveryCircle.color.a, -- Color
                        false, false, 2, false, nil, nil, false -- Other settings
                    )

                    -- Show distance to delivery area if player is close and UI is enabled
                    if Config.DeliveryCircle.showDistanceUI and distance <= Config.DeliveryCircle.radius + 20.0 then
                        local distanceToCenter = math.floor(distance)
                        local isInside = distance <= Config.DeliveryCircle.radius

                        if isInside and Config.DeliveryCircle.showInsideUI then
                            -- Player is inside the delivery area
                            lib.showTextUI('[E] تسليم الطلب - أنت داخل منطقة التسليم', {
                                position = "top-center",
                                icon = 'pizza-slice',
                                style = {
                                    borderRadius = 10,
                                    backgroundColor = '#28a745',
                                    color = 'white'
                                }
                            })
                        elseif not isInside then
                            -- Player is outside but close
                            lib.showTextUI(string.format('المسافة إلى منطقة التسليم: %dm', distanceToCenter - Config.DeliveryCircle.radius), {
                                position = "top-center",
                                icon = 'location-arrow',
                                style = {
                                    borderRadius = 10,
                                    backgroundColor = '#ffc107',
                                    color = 'black'
                                }
                            })
                        end
                    else
                        lib.hideTextUI()
                    end
                else
                    lib.hideTextUI()
                end

                Wait(100) -- Update every 100ms for smooth experience
            end
            lib.hideTextUI() -- Hide when thread ends
        end)
    end
end



function removePreviousMarkerAndTarget()
    if targetZoneId then
        exports.ox_target:removeZone(targetZoneId)
        targetZoneId = nil
    end

    if currentDeliveryBlip then
        RemoveBlip(currentDeliveryBlip)
        currentDeliveryBlip = nil
    end

    removeDeliveryCircle()
    removeDeliveryBot()
end

function createDeliveryBlip(delivery)
    local deliveryCoords = delivery.location
    removePreviousMarkerAndTarget()

    -- Create main delivery blip
    currentDeliveryBlip = AddBlipForCoord(deliveryCoords.x, deliveryCoords.y, deliveryCoords.z)
    SetBlipSprite(currentDeliveryBlip, 1)
    SetBlipColour(currentDeliveryBlip, 5)
    SetBlipScale(currentDeliveryBlip, 0.8)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("موقع التسليم")
    EndTextCommandSetBlipName(currentDeliveryBlip)

    -- Create yellow circle around delivery location
    createDeliveryCircle(deliveryCoords)

    -- Create delivery bot instead of target zone
    currentDeliveryBot = createDeliveryBot(deliveryCoords, delivery.heading)

    -- Show notification about the delivery area
    if Config.DeliveryCircle.enabled and Config.DeliveryCircle.showNotification then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', string.format('توجه إلى المنطقة الصفراء لتسليم الطلب (نطاق %dm)', Config.DeliveryCircle.radius), 'primary', Config.DeliveryCircle.notificationDuration)
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', string.format('توجه إلى المنطقة الصفراء لتسليم الطلب (نطاق %dm)', Config.DeliveryCircle.radius), 'info', Config.DeliveryCircle.notificationDuration)
        end
    end
end

function startDeliveryAnimation()
    -- Security Check 1: Cooldown protection
    local currentTime = GetGameTimer()
    if currentTime - lastDeliveryTime < deliveryCooldown then
        local remainingTime = math.ceil((deliveryCooldown - (currentTime - lastDeliveryTime)) / 1000)
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', string.format('انتظر %d ثانية قبل التسليم التالي!', remainingTime), 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', string.format('انتظر %d ثانية قبل التسليم التالي!', remainingTime), 'error')
        end
        return
    end

    -- Security Check 2: Player state validation
    local isValidState, stateError = isPlayerStateValid()
    if not isValidState then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', stateError, 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', stateError, 'error')
        end
        return
    end

    -- Security Check 3: Distance validation
    if not isPlayerNearDeliveryLocation() then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'يجب أن تكون قريباً من موقع التسليم!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'يجب أن تكون قريباً من موقع التسليم!', 'error')
        end
        return
    end

    -- Security Check 4: Vehicle validation
    if not hasCorrectDeliveryVehicle() then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'يجب أن تكون بالقرب من مركبة التوصيل!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'يجب أن تكون بالقرب من مركبة التوصيل!', 'error')
        end
        return
    end

    -- Security Check 5: Job access validation
    if not hasJobAccess then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'ليس لديك صلاحية للوصول لهذه الوظيفة!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'ليس لديك صلاحية للوصول لهذه الوظيفة!', 'error')
        end
        return
    end

    -- Security Check 6: Delivery state validation
    if not isDelivering or not currentDelivery then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'لست في حالة توصيل نشطة!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'لست في حالة توصيل نشطة!', 'error')
        end
        return
    end

    local playerPed = PlayerPedId()
    local animDict = "anim@heists@box_carry@"
    local animName = "idle"
    local prop = "prop_pizza_box_02"
    local propBone = 28422
    local propPlacement = {0.0100, -0.1000, -0.1590, 20.0000007, 0.0, 0.0}

    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end

    local propEntity = CreateObject(GetHashKey(prop), GetEntityCoords(playerPed), true, true, false)
    AttachEntityToEntity(propEntity, playerPed, GetPedBoneIndex(playerPed, propBone),
        propPlacement[1], propPlacement[2], propPlacement[3],
        propPlacement[4], propPlacement[5], propPlacement[6],
        true, true, false, true, 1, true
    )

    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, -1, 49, 0, false, false, false)

    if lib.progressCircle({
        duration = Config.DeliveryTime or 3000,
        position = 'bottom',
        label = 'جاري تسليم الطلب',
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true,
            combat = true,
            mouse = false
        }
    }) then
        ClearPedTasks(playerPed)
        DetachEntity(propEntity, true, true)
        DeleteObject(propEntity)

        -- Update last delivery time for cooldown
        lastDeliveryTime = GetGameTimer()

        completeDelivery()
    end
end

function completeDelivery()
    -- Update vehicle usage time to reset inactivity timer
    lastVehicleUseTime = GetGameTimer()
    deliveryStartTime = GetGameTimer() -- Reset delivery start time for next delivery
    lastDeliveryCompletionTime = GetGameTimer() -- Update last delivery completion time

    TriggerServerEvent('neon_pizzajob:completeDelivery')

    if Config.Framework == 'QB' then
        TriggerEvent('QBCore:Notify', 'تم تسليم الطلب بنجاح. انتقل إلى الطلب التالي.', 'success')
    elseif Config.Framework == 'ESX' then
        TriggerEvent('esx:showNotification', 'تم تسليم الطلب بنجاح. انتقل إلى الطلب التالي.', 'success')
    end

    local newDelivery
    repeat
        newDelivery = getRandomDeliveryLocation()
    until newDelivery ~= currentDelivery

    currentDelivery = newDelivery

    -- Send new delivery location to server for distance calculation
    TriggerServerEvent('pizzajob:setDeliveryLocation', currentDelivery.location)

    setDeliveryWaypoint(currentDelivery)
    createDeliveryBlip(currentDelivery)
end


function stopDelivering()
    local playerVehicle = playerVehicles[PlayerPedId()]

    if playerVehicle and DoesEntityExist(playerVehicle) then
        DeleteVehicle(playerVehicle)
        playerVehicles[PlayerPedId()] = nil
    end

    -- Reset delivery vehicle status
    hasActiveDeliveryVehicle = false
    currentDeliveryVehicle = nil

    -- Reset vehicle protection data
    vehicleOwner = nil
    lastVehicleUseTime = 0
    deliveryStartTime = 0
    lastDeliveryCompletionTime = 0

    if Config.Framework == 'QB' then
        TriggerEvent('QBCore:Notify', 'لقد توقفت عن الدلفري. تم إزالة الدباب.', 'primary')
    elseif Config.Framework == 'ESX' then
        TriggerEvent('esx:showNotification', 'لقد توقفت عن الدلفري. تم إزالة الدباب.', 'info')
    end

    removePreviousMarkerAndTarget()
    removeDeliveryBot()
    removeDeliveryCircle() -- Make sure circle is removed
    SetWaypointOff()
    TriggerServerEvent('pizzajob:stopDelivering')
    isDelivering = false
    currentDelivery = nil

    -- Note: Original clothes will be restored by server event 'pizzajob:restoreClothes'
end

local function viewLeaderboard()
    TriggerServerEvent('pizzajob:requestLeaderboard')
end

local function displayLeaderboardMenu(leaderboardData)
    local leaderboardOptions = {}

    for i, playerData in ipairs(leaderboardData) do
        table.insert(leaderboardOptions, {
            title = string.format("المرتبة %d: %s %s", i, playerData.firstname, playerData.lastname),
            description = string.format("إجمالي التوصيلات: %d", playerData.total_deliveries),
            icon = 'fa-solid fa-user'
        })
    end

    lib.registerContext({
        id = 'leaderboard_menu',
        title = 'أفضل عمال الدلفري',
        options = leaderboardOptions,
        onExit = function() end
    })

    lib.showContext('leaderboard_menu')
end

RegisterNetEvent('pizzajob:receiveLeaderboard', function(leaderboardData)
    displayLeaderboardMenu(leaderboardData)
end)



local pendingAction = nil
local pizzaBlip = nil

local function createBlip()
    if hasJobAccess and not pizzaBlip then
        pizzaBlip = AddBlipForCoord(Config.Ped.location.x, Config.Ped.location.y, Config.Ped.location.z)
        SetBlipSprite(pizzaBlip, Config.Blip.sprite)
        SetBlipColour(pizzaBlip, Config.Blip.color)
        SetBlipScale(pizzaBlip, Config.Blip.size)
        SetBlipAsShortRange(pizzaBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(Config.Blip.label)
        EndTextCommandSetBlipName(pizzaBlip)
    elseif not hasJobAccess and pizzaBlip then
        RemoveBlip(pizzaBlip)
        pizzaBlip = nil
    end
end

local function updateBlipVisibility()
    createBlip()
end

local function actuallyOpenChefMenu()
    local options = {}

    if isDelivering then
        table.insert(options, {
            title = 'إيقاف التوصيل',
            description = 'توقف عن الدلفري وإرجاع الدباب.',
            icon = 'fa-solid fa-stop',
            iconColor = '#FF0000',
            onSelect = function()
                stopDelivering()
            end
        })
    else
        table.insert(options, {
            title = 'بدء التوصيل',
            description = 'ابدأ الدلفري في أنحاء المدينة.',
            icon = 'fa-solid fa-play',
            iconColor = '#00FF00',
            onSelect = function()
                pendingAction = "start_delivery"
                TriggerServerEvent('pizzajob:checkJob')
            end
        })
    end

    table.insert(options, {
        title = 'عرض المتصدرين',
        description = 'شاهد أفضل عمال الدلفري!',
        icon = 'fa-solid fa-trophy',
        onSelect = function()
            viewLeaderboard()
        end
    })

    -- إضافة خيار استعادة الملابس الأصلية
    if Config.Uniform.enabled and originalClothes ~= nil then
        table.insert(options, {
            title = 'استعادة ملابسك الأصلية',
            description = 'استعد ملابسك الأصلية قبل بدء العمل.',
            icon = 'fa-solid fa-undo',
            iconColor = '#FFA500',
            onSelect = function()
                restoreOriginalClothes()
            end
        })
    end

    lib.registerContext({
        id = 'chef_menu',
        title = 'الدلفري',
        options = options
    })

    lib.showContext('chef_menu')
end

-- Job check result handler
RegisterNetEvent('pizzajob:jobCheckResult', function(hasJob)
    local previousJobAccess = hasJobAccess
    hasJobAccess = hasJob

    -- Update blip visibility based on job access
    if previousJobAccess ~= hasJobAccess then
        updateBlipVisibility()
    end

    -- If job access changed and we have a delivery bot, recreate it
    if previousJobAccess ~= hasJobAccess and currentDelivery and isDelivering then
        removeDeliveryBot()
        if hasJobAccess then
            currentDeliveryBot = createDeliveryBot(currentDelivery.location, currentDelivery.heading)
        end
    end

    -- Handle vehicle access based on job status
    if hasJob then
        -- Player has job access, unlock nearby pizza vehicles for them
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local vehicles = GetGamePool('CVehicle')

        for _, vehicle in ipairs(vehicles) do
            if DoesEntityExist(vehicle) then
                local vehicleCoords = GetEntityCoords(vehicle)
                local distance = #(playerCoords - vehicleCoords)

                if distance <= 5.0 then -- Within 5 meters
                    local plate = GetVehicleNumberPlateText(vehicle)
                    if plate and string.find(plate, "PIZZA") then
                        SetVehicleDoorsLocked(vehicle, 0) -- Unlock for job holders
                    end
                end
            end
        end
    end

    if pendingAction == "start_delivery" and hasJob then
        startDelivering()
    elseif pendingAction == "open_menu" and hasJob then
        actuallyOpenChefMenu()
    elseif pendingAction ~= "job_check" and not hasJob then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', 'تحتاج إلى وظيفة الدلفري للوصول إلى هذا!', 'error')
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', 'تحتاج إلى وظيفة الدلفري للوصول إلى هذا!', 'error')
        end
    end
    pendingAction = nil
end)

local function openChefMenu()
    -- Check job first
    pendingAction = "open_menu"
    TriggerServerEvent('pizzajob:checkJob')
end

-- نظام جديد للكشف عن القرب والضغط على E
local chefPed = nil
local nearChef = false
local nearDeliveryBot = false

-- وظيفة للكشف عن القرب من الطباخ
local function checkNearChef()
    Citizen.CreateThread(function()
        while true do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)

            if chefPed and DoesEntityExist(chefPed) then
                local chefCoords = GetEntityCoords(chefPed)
                local distance = #(playerCoords - chefCoords)

                if distance <= Config.TargetSettings.distance and hasJobAccess and not IsPedInAnyVehicle(playerPed, false) and not IsEntityDead(playerPed) then
                    if not nearChef then
                        nearChef = true
                        lib.showTextUI('[E] ' .. Config.TargetSettings.label, {
                            position = "top-center",
                            icon = 'pizza-slice',
                            style = {
                                borderRadius = 10,
                                backgroundColor = '#48CAE4',
                                color = 'white'
                            }
                        })
                    end
                else
                    if nearChef then
                        nearChef = false
                        lib.hideTextUI()
                    end
                end
            else
                if nearChef then
                    nearChef = false
                    lib.hideTextUI()
                end
            end

            Wait(100)
        end
    end)
end

-- وظيفة للكشف عن القرب من بوت التسليم
local function checkNearDeliveryBot()
    Citizen.CreateThread(function()
        while true do
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)

            if currentDeliveryBot and DoesEntityExist(currentDeliveryBot) and isDelivering and hasJobAccess then
                local botCoords = GetEntityCoords(currentDeliveryBot)
                local distance = #(playerCoords - botCoords)

                if distance <= Config.DeliveryBot.distance and not IsPedInAnyVehicle(playerPed, false) then
                    if not nearDeliveryBot then
                        nearDeliveryBot = true
                        lib.showTextUI('[E] ' .. Config.DeliveryBot.label, {
                            position = "top-center",
                            icon = 'pizza-slice',
                            style = {
                                borderRadius = 10,
                                backgroundColor = '#28a745',
                                color = 'white'
                            }
                        })
                    end
                else
                    if nearDeliveryBot then
                        nearDeliveryBot = false
                        lib.hideTextUI()
                    end
                end
            else
                if nearDeliveryBot then
                    nearDeliveryBot = false
                    lib.hideTextUI()
                end
            end

            Wait(100)
        end
    end)
end

-- وظيفة للتعامل مع الضغط على E
local function handleKeyPress()
    Citizen.CreateThread(function()
        while true do
            if IsControlJustReleased(0, 38) then -- E key
                if nearChef then
                    openChefMenu()
                elseif nearDeliveryBot then
                    startDeliveryAnimation()
                end
            end
            Wait(0)
        end
    end)
end

local function spawnPed()
    local pedHash = GetHashKey(pedModel)

    Citizen.CreateThread(function()
        RequestModel(pedHash)
        local timeout = 5000
        while not HasModelLoaded(pedHash) do
            Wait(500)
            timeout = timeout - 500
        end

        if HasModelLoaded(pedHash) then
            local pedZ = Config.Ped.location.z - 1.0
            chefPed = CreatePed(4, pedHash, Config.Ped.location.x, Config.Ped.location.y, pedZ, Config.Ped.location.w, false, true)
            SetEntityInvincible(chefPed, true)
            FreezeEntityPosition(chefPed, true)
            SetBlockingOfNonTemporaryEvents(chefPed, true)
        end
    end)
end

-- Periodic job check to update bot visibility
local function startJobChecker()
    Citizen.CreateThread(function()
        while true do
            Wait(5000) -- Check every 5 seconds
            if not pendingAction then -- Only check if not already checking
                pendingAction = "job_check"
                TriggerServerEvent('pizzajob:checkJob')
            end
        end
    end)
end

-- Event handler for showing XP notification
RegisterNetEvent('pizzajob:showXP')
AddEventHandler('pizzajob:showXP', function(xpAmount, xpType, distance)
    if Config.XPNotification.enabled and xpAmount and xpAmount > 0 then
        -- Create XP message using config
        local message = string.format('🎉 حصلت على %d نقطة خبرة!', xpAmount)
        local detailedMessage

        -- Use distance message if distance is provided and enabled
        if distance and Config.DistanceRewards.showDistance then
            detailedMessage = string.format(Config.XPNotification.distanceMessage, xpAmount, distance)
        else
            detailedMessage = string.format(Config.XPNotification.message, xpAmount)
        end

        -- Use appropriate notification system based on framework
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', message, 'success', Config.XPNotification.duration)
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', message, 'success', Config.XPNotification.duration)
        end

        -- XP notification disabled - using only framework notifications

        print(string.format("^2[Pizza Job] Player received %d XP points for %s!^7", xpAmount, xpType or 'delivery'))
    end
end)

-- Event handler for showing payment notification with distance
RegisterNetEvent('pizzajob:showPayment')
AddEventHandler('pizzajob:showPayment', function(payAmount, distance)
    if payAmount and payAmount > 0 then
        local message
        if distance and Config.DistanceRewards.showDistance then
            message = string.format('💰 حصلت على $%d! (مسافة: %.0f متر)', payAmount, distance)
        else
            message = string.format('💰 حصلت على $%d!', payAmount)
        end

        -- Payment notification disabled - using only framework notifications

        print(string.format("^2[Pizza Job] Player received $%d for delivery!^7", payAmount))
    end
end)

-- Event to force player out of unauthorized vehicle
RegisterNetEvent('pizzajob:forceExitVehicle')
AddEventHandler('pizzajob:forceExitVehicle', function()
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)

    if vehicle ~= 0 then
        TaskLeaveVehicle(playerPed, vehicle, 4160) -- Force exit immediately
        ClearPedTasksImmediately(playerPed)
        -- Add a small delay to ensure the player is fully out
        Citizen.SetTimeout(1000, function()
            ClearPedTasksImmediately(playerPed)
        end)
    end
end)

-- New event to prevent vehicle entry
RegisterNetEvent('pizzajob:preventVehicleEntry')
AddEventHandler('pizzajob:preventVehicleEntry', function()
    local playerPed = PlayerPedId()
    -- Clear any entry tasks immediately
    ClearPedTasksImmediately(playerPed)
    -- Make sure player is not in any vehicle
    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        TaskLeaveVehicle(playerPed, vehicle, 4160)
        Citizen.SetTimeout(500, function()
            ClearPedTasksImmediately(playerPed)
        end)
    end
end)

-- Variable to store original clothes
local originalClothes = nil

-- Function to save current clothes
local function saveOriginalClothes()
    local playerPed = PlayerPedId()

    originalClothes = {
        torso = { drawable = GetPedDrawableVariation(playerPed, 11), texture = GetPedTextureVariation(playerPed, 11) },
        arms = { drawable = GetPedDrawableVariation(playerPed, 3), texture = GetPedTextureVariation(playerPed, 3) },
        legs = { drawable = GetPedDrawableVariation(playerPed, 4), texture = GetPedTextureVariation(playerPed, 4) },
        shoes = { drawable = GetPedDrawableVariation(playerPed, 6), texture = GetPedTextureVariation(playerPed, 6) },
        hat = { drawable = GetPedPropIndex(playerPed, 0), texture = GetPedPropTextureIndex(playerPed, 0) }
    }

    print("^3[Pizza Job] Original clothes saved^7")
end

-- Function to restore original clothes
local function restoreOriginalClothes()
    if not originalClothes then
        print("^1[Pizza Job] No original clothes saved^7")
        return
    end

    local playerPed = PlayerPedId()

    -- Restore original clothes
    SetPedComponentVariation(playerPed, 11, originalClothes.torso.drawable, originalClothes.torso.texture, 0)
    SetPedComponentVariation(playerPed, 3, originalClothes.arms.drawable, originalClothes.arms.texture, 0)
    SetPedComponentVariation(playerPed, 4, originalClothes.legs.drawable, originalClothes.legs.texture, 0)
    SetPedComponentVariation(playerPed, 6, originalClothes.shoes.drawable, originalClothes.shoes.texture, 0)

    if originalClothes.hat.drawable ~= -1 then
        SetPedPropIndex(playerPed, 0, originalClothes.hat.drawable, originalClothes.hat.texture, true)
    else
        ClearPedProp(playerPed, 0)
    end

    print("^2[Pizza Job] Original clothes restored^7")
end

-- Function to apply pizza job uniform
local function applyPizzaUniform(saveClothes)
    if not Config.Uniform.enabled then
        return
    end

    local playerPed = PlayerPedId()
    local playerModel = GetEntityModel(playerPed)

    -- Save original clothes before applying uniform (only if requested)
    if saveClothes and not originalClothes then
        saveOriginalClothes()
    end

    -- Check if player is male or female
    local isMale = (playerModel == GetHashKey("mp_m_freemode_01"))
    local uniformConfig = isMale and Config.Uniform.male or Config.Uniform.female

    print("^3[Pizza Job] Applying pizza uniform...^7")

    -- Apply uniform components
    -- Torso (shirt/jacket)
    SetPedComponentVariation(playerPed, 11, uniformConfig.torso.drawable, uniformConfig.torso.texture, 0)

    -- Arms/Hands (تم تغييرها إلى 40)
    SetPedComponentVariation(playerPed, 3, uniformConfig.arms.drawable, uniformConfig.arms.texture, 0)

    -- Legs (pants)
    SetPedComponentVariation(playerPed, 4, uniformConfig.legs.drawable, uniformConfig.legs.texture, 0)

    -- Shoes
    SetPedComponentVariation(playerPed, 6, uniformConfig.shoes.drawable, uniformConfig.shoes.texture, 0)

    -- Hat/Cap
    SetPedPropIndex(playerPed, 0, uniformConfig.hat.drawable, uniformConfig.hat.texture, true)

    -- Show notification
    if Config.Uniform.notification.enabled then
        if Config.Framework == 'QB' then
            TriggerEvent('QBCore:Notify', Config.Uniform.notification.message, 'success', Config.Uniform.notification.duration)
        elseif Config.Framework == 'ESX' then
            TriggerEvent('esx:showNotification', Config.Uniform.notification.message, 'success', Config.Uniform.notification.duration)
        end
    end

    print("^2[Pizza Job] Pizza uniform applied successfully!^7")
end

-- Event handler for receiving uniform
RegisterNetEvent('pizzajob:giveUniform')
AddEventHandler('pizzajob:giveUniform', function()
    -- Save original clothes before applying uniform
    if not originalClothes then
        saveOriginalClothes()
    end
    applyPizzaUniform(false) -- Apply uniform without saving clothes again
end)

-- Event handler for restoring original clothes
RegisterNetEvent('pizzajob:restoreClothes')
AddEventHandler('pizzajob:restoreClothes', function()
    restoreOriginalClothes()

    -- Show notification when clothes are restored
    if Config.Framework == 'QB' then
        TriggerEvent('QBCore:Notify', 'تم استعادة ملابسك الأصلية!', 'success', 3000)
    elseif Config.Framework == 'ESX' then
        TriggerEvent('esx:showNotification', 'تم استعادة ملابسك الأصلية!', 'success', 3000)
    end

    -- Clear saved clothes after restoration
    originalClothes = nil
end)

AddEventHandler('onClientResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        spawnPed()
        startJobChecker()
        checkNearChef()
        checkNearDeliveryBot()
        handleKeyPress()
        -- Blip will be created by job checker when player has the job
    end
end)